#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module principal.
"""

import unittest
import asyncio
import logging
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from io import StringIO

from ..main import setup_logging, setup_logging_legacy, amain
from ..config import (
    ConfluenceConfig, SearchCriteria, StorageConfig,
    ProcessingConfig, LoggingConfig
)
from ..logging_utils import CorrelationContext


class TestSetupLogging(unittest.TestCase):
    """Tests pour la fonction setup_logging."""

    def setUp(self):
        """Configuration des tests."""
        # Sauvegarder l'état initial du logging
        self.original_handlers = logging.getLogger().handlers[:]
        self.original_level = logging.getLogger().level

    def tearDown(self):
        """Nettoyage après les tests."""
        # Restaurer l'état initial du logging
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        for handler in self.original_handlers:
            root_logger.addHandler(handler)
        root_logger.setLevel(self.original_level)

    def test_setup_logging_default_config(self):
        """Test de configuration du logging avec config par défaut."""
        setup_logging()

        root_logger = logging.getLogger()
        self.assertGreaterEqual(len(root_logger.handlers), 1)
        self.assertEqual(root_logger.level, logging.INFO)

    def test_setup_logging_custom_config(self):
        """Test de configuration du logging avec config personnalisée."""
        config = LoggingConfig(
            level="DEBUG",
            structured=True,
            enable_console=True,
            enable_file=False,
            enable_security_filter=True,
            enable_correlation_id=True
        )

        setup_logging(config)

        root_logger = logging.getLogger()
        self.assertEqual(root_logger.level, logging.DEBUG)
        self.assertGreaterEqual(len(root_logger.handlers), 1)

    def test_setup_logging_with_file(self):
        """Test de configuration du logging avec fichier."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            config = LoggingConfig(
                level="INFO",
                enable_console=False,
                enable_file=True,
                file_path=temp_path
            )

            setup_logging(config)

            # Vérifier qu'un handler de fichier a été ajouté
            root_logger = logging.getLogger()
            file_handlers = [h for h in root_logger.handlers
                           if hasattr(h, 'baseFilename')]
            self.assertGreater(len(file_handlers), 0)

        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_setup_logging_valid_levels(self):
        """Test de configuration avec différents niveaux de log valides."""
        # Tester différents niveaux valides
        test_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

        for level_name in test_levels:
            with self.subTest(level=level_name):
                config = LoggingConfig(level=level_name)
                setup_logging(config)

                root_logger = logging.getLogger()
                expected_level = getattr(logging, level_name)
                self.assertEqual(root_logger.level, expected_level)

    def test_setup_logging_structured_format(self):
        """Test de configuration avec format structuré."""
        config = LoggingConfig(
            structured=True,
            include_traceback=True
        )

        setup_logging(config)

        # Vérifier qu'un handler a été configuré
        root_logger = logging.getLogger()
        self.assertGreater(len(root_logger.handlers), 0)

    def test_setup_logging_suppress_external(self):
        """Test de suppression des loggers externes."""
        config = LoggingConfig(
            suppress_external_loggers=True,
            external_logger_level="ERROR"
        )

        setup_logging(config)

        # Vérifier que les loggers externes ont été configurés
        urllib3_logger = logging.getLogger("urllib3")
        self.assertEqual(urllib3_logger.level, logging.ERROR)


class TestSetupLoggingLegacy(unittest.TestCase):
    """Tests pour la fonction setup_logging_legacy."""

    def setUp(self):
        """Configuration des tests."""
        # Sauvegarder l'état initial du logging
        self.original_handlers = logging.getLogger().handlers[:]
        self.original_level = logging.getLogger().level

    def tearDown(self):
        """Nettoyage après les tests."""
        # Restaurer l'état initial du logging
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        for handler in self.original_handlers:
            root_logger.addHandler(handler)
        root_logger.setLevel(self.original_level)

    def test_setup_logging_legacy_default(self):
        """Test de configuration legacy avec paramètres par défaut."""
        setup_logging_legacy()

        root_logger = logging.getLogger()
        self.assertGreaterEqual(len(root_logger.handlers), 1)

    def test_setup_logging_legacy_custom_params(self):
        """Test de configuration legacy avec paramètres personnalisés."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            setup_logging_legacy(
                log_level="DEBUG",
                log_file=temp_path,
                structured=True
            )

            root_logger = logging.getLogger()
            self.assertEqual(root_logger.level, logging.DEBUG)

        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    @patch.dict(os.environ, {
        'LOG_LEVEL': 'WARNING',
        'LOG_FILE': 'test.log',
        'STRUCTURED_LOGGING': 'false'
    })
    def test_setup_logging_legacy_env_vars(self):
        """Test de configuration legacy avec variables d'environnement."""
        setup_logging_legacy()

        root_logger = logging.getLogger()
        self.assertEqual(root_logger.level, logging.WARNING)


class TestAmain(unittest.TestCase):
    """Tests pour la fonction amain."""

    def setUp(self):
        """Configuration des tests."""
        CorrelationContext.clear_correlation_id()

    def tearDown(self):
        """Nettoyage après les tests."""
        CorrelationContext.clear_correlation_id()

    @patch('confluence_rag.main.SyncOrchestrator')
    @patch('confluence_rag.main.setup_logging')
    def test_amain_basic(self, mock_setup_logging, mock_orchestrator_class):
        """Test de la fonction amain basique."""
        # Configuration des mocks
        mock_orchestrator = AsyncMock()
        mock_orchestrator.run = AsyncMock()
        mock_orchestrator.stats = {}
        mock_orchestrator_class.return_value = mock_orchestrator

        # Configuration de test
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token"
        )
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        async def run_test():
            await amain(config, criteria)

        # Exécuter le test
        asyncio.run(run_test())

        # Vérifications
        mock_setup_logging.assert_called_once()
        mock_orchestrator_class.assert_called_once()
        mock_orchestrator.run.assert_called_once()

    @patch('confluence_rag.main.SyncOrchestrator')
    @patch('confluence_rag.main.setup_logging')
    def test_amain_with_all_configs(self, mock_setup_logging, mock_orchestrator_class):
        """Test de amain avec toutes les configurations."""
        # Configuration des mocks
        mock_orchestrator = AsyncMock()
        mock_orchestrator.run = AsyncMock()
        mock_orchestrator.stats = {}
        mock_orchestrator_class.return_value = mock_orchestrator

        # Configurations de test
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token"
        )
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)
        storage_config = StorageConfig(storage_type="filesystem")
        processing_config = ProcessingConfig(max_parallel_downloads=2)
        logging_config = LoggingConfig(level="DEBUG")

        async def run_test():
            await amain(
                config,
                criteria,
                storage_config,
                processing_config,
                logging_config
            )

        # Exécuter le test
        asyncio.run(run_test())

        # Vérifications
        mock_setup_logging.assert_called_once_with(logging_config)
        mock_orchestrator_class.assert_called_once_with(
            config, criteria, storage_config, processing_config
        )

    @patch('confluence_rag.main.StorageConfig.from_env')
    @patch('confluence_rag.main.ProcessingConfig.from_env')
    @patch('confluence_rag.main.SyncOrchestrator')
    @patch('confluence_rag.main.setup_logging')
    def test_amain_default_configs(self, mock_setup_logging, mock_orchestrator_class,
                                   mock_processing_from_env, mock_storage_from_env):
        """Test de amain avec configurations par défaut."""
        # Configuration des mocks
        mock_orchestrator = AsyncMock()
        mock_orchestrator.run = AsyncMock()
        mock_orchestrator.stats = {}
        mock_orchestrator_class.return_value = mock_orchestrator

        mock_storage_config = StorageConfig(storage_type="filesystem")
        mock_processing_config = ProcessingConfig()
        mock_storage_from_env.return_value = mock_storage_config
        mock_processing_from_env.return_value = mock_processing_config

        # Configuration de test
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token"
        )
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        async def run_test():
            await amain(config, criteria)

        # Exécuter le test
        asyncio.run(run_test())

        # Vérifications
        mock_storage_from_env.assert_called_once()
        mock_processing_from_env.assert_called_once()
        mock_orchestrator_class.assert_called_once_with(
            config, criteria, mock_storage_config, mock_processing_config
        )

    @patch('confluence_rag.main.SyncOrchestrator')
    @patch('confluence_rag.main.setup_logging')
    def test_amain_correlation_id(self, mock_setup_logging, mock_orchestrator_class):
        """Test que amain génère et utilise un correlation ID."""
        # Configuration des mocks
        mock_orchestrator = AsyncMock()
        mock_orchestrator.run = AsyncMock()
        mock_orchestrator.stats = {}
        mock_orchestrator_class.return_value = mock_orchestrator

        # Configuration de test
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token"
        )
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        async def run_test():
            # Vérifier qu'aucun ID n'est défini avant l'appel
            self.assertIsNone(CorrelationContext.get_correlation_id())

            await amain(config, criteria)

            # Vérifier que l'ID est nettoyé après l'appel (grâce au décorateur)
            self.assertIsNone(CorrelationContext.get_correlation_id())

        # Exécuter le test
        asyncio.run(run_test())

        # Vérifier que l'orchestrateur a reçu un correlation_id
        self.assertIn("correlation_id", mock_orchestrator.stats)
        self.assertIsNotNone(mock_orchestrator.stats["correlation_id"])


if __name__ == '__main__':
    unittest.main()
