#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module d'authentification.
"""

import unittest
from unittest.mock import Mock, patch
import aiohttp
from pydantic import SecretStr

from ..auth import AuthenticationManager
from ..config import ConfluenceConfig
from ..constants import AuthType, APIConstants
from ..exceptions import AuthenticationError


class TestAuthenticationManager(unittest.TestCase):
    """Tests pour la classe AuthenticationManager."""

    def setUp(self):
        """Configuration des tests."""
        self.base_url = "https://test.atlassian.net"

    def test_pat_token_authentication(self):
        """Test de l'authentification avec PAT token."""
        config = ConfluenceConfig(
            url=self.base_url,
            pat_token=SecretStr("test_pat_token_123456789")
        )

        auth_manager = AuthenticationManager(config)

        # Vérifier le type d'authentification détecté
        self.assertEqual(auth_manager._auth_type, AuthType.PAT_TOKEN)

        # Vérifier les headers et auth
        headers, auth = auth_manager.get_auth_headers_and_auth()

        self.assertIn("Authorization", headers)
        self.assertEqual(headers["Authorization"], "Bearer test_pat_token_123456789")
        self.assertEqual(headers["Accept"], APIConstants.JSON_CONTENT_TYPE)
        self.assertIsNone(auth)

    def test_api_token_authentication(self):
        """Test de l'authentification avec API token classique."""
        config = ConfluenceConfig(
            url=self.base_url,
            username="test_user",
            api_token=SecretStr("test_api_token_123456789")
        )

        auth_manager = AuthenticationManager(config)

        # Vérifier le type d'authentification détecté
        self.assertEqual(auth_manager._auth_type, AuthType.API_TOKEN)

        # Vérifier les headers et auth
        headers, auth = auth_manager.get_auth_headers_and_auth()

        self.assertNotIn("Authorization", headers)
        self.assertEqual(headers["Accept"], APIConstants.JSON_CONTENT_TYPE)
        self.assertIsInstance(auth, aiohttp.BasicAuth)
        self.assertEqual(auth.login, "test_user")
        self.assertEqual(auth.password, "test_api_token_123456789")

    def test_invalid_authentication_no_tokens(self):
        """Test d'erreur avec aucun token fourni."""
        from pydantic import ValidationError

        # La validation Pydantic doit empêcher la création de la config sans tokens
        with self.assertRaises(ValidationError) as context:
            config = ConfluenceConfig(url=self.base_url)

        # Vérifier que l'erreur mentionne les tokens requis
        self.assertIn("Vous devez fournir soit un PAT token, soit un API token", str(context.exception))

    def test_invalid_authentication_api_token_without_username(self):
        """Test d'erreur avec API token mais sans username."""
        config = ConfluenceConfig(
            url=self.base_url,
            api_token=SecretStr("test_api_token")
        )

        with self.assertRaises(AuthenticationError) as context:
            AuthenticationManager(config)

        self.assertIn("Configuration d'authentification invalide", str(context.exception))

    def test_pat_token_priority_over_api_token(self):
        """Test que le PAT token a la priorité sur l'API token."""
        config = ConfluenceConfig(
            url=self.base_url,
            username="test_user",
            api_token=SecretStr("test_api_token"),
            pat_token=SecretStr("test_pat_token")
        )

        auth_manager = AuthenticationManager(config)

        # Le PAT token doit avoir la priorité
        self.assertEqual(auth_manager._auth_type, AuthType.PAT_TOKEN)

        headers, auth = auth_manager.get_auth_headers_and_auth()
        self.assertIn("Authorization", headers)
        self.assertEqual(headers["Authorization"], "Bearer test_pat_token")
        self.assertIsNone(auth)

    @patch('kbotloadscheduler.loader.confluence.auth.logging.getLogger')
    def test_logging_configuration(self, mock_get_logger):
        """Test de la configuration du logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        config = ConfluenceConfig(
            url=self.base_url,
            pat_token=SecretStr("test_pat_token")
        )

        auth_manager = AuthenticationManager(config)

        # Vérifier que le logger est configuré correctement
        mock_get_logger.assert_called_with(f"{auth_manager.__module__}.{auth_manager.__class__.__name__}")

        # Vérifier que les messages de log appropriés sont appelés
        mock_logger.info.assert_called_with("Configuration pour Personal Access Token (PAT) détectée.")

    def test_auth_headers_immutability(self):
        """Test que les headers retournés sont indépendants entre les appels."""
        config = ConfluenceConfig(
            url=self.base_url,
            pat_token=SecretStr("test_pat_token")
        )

        auth_manager = AuthenticationManager(config)

        headers1, _ = auth_manager.get_auth_headers_and_auth()
        headers2, _ = auth_manager.get_auth_headers_and_auth()

        # Modifier un des dictionnaires ne doit pas affecter l'autre
        headers1["Custom-Header"] = "test"

        self.assertNotIn("Custom-Header", headers2)
        self.assertEqual(len(headers2), 2)  # Accept + Authorization


if __name__ == '__main__':
    unittest.main()
