#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test rapide de la pagination parallèle du client Confluence.
"""

import asyncio
import logging
import time
from ..client import ConfluenceClient
from ..config import ConfluenceConfig, SearchCriteria
from pydantic import SecretStr

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_parallel_pagination():
    """Test rapide de la pagination parallèle."""
    logger = logging.getLogger(__name__)

    try:
        # Configuration de test avec pagination parallèle
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
            enable_parallel_pagination=True,
            max_parallel_requests=3,
            parallel_pagination_threshold=50  # Seuil bas pour forcer la parallélisation
        )

        logger.info("Configuration de pagination parallèle:")
        logger.info(f"  - Activée: {config.enable_parallel_pagination}")
        logger.info(f"  - Requêtes parallèles max: {config.max_parallel_requests}")
        logger.info(f"  - Seuil d'activation: {config.parallel_pagination_threshold}")

        # Test avec context manager
        async with ConfluenceClient(config) as client:
            logger.info("=== Test de pagination parallèle ===")

            # Critères pour déclencher la pagination parallèle
            criteria = SearchCriteria(
                spaces=["EXAMPLE"],  # Remplacez par un espace existant
                types=["page"],
                max_results=150  # Au-dessus du seuil pour déclencher la parallélisation
            )

            logger.info(f"Critères de recherche: {criteria.max_results} résultats max")

            # Mesurer le temps d'exécution
            start_time = time.time()

            # Effectuer la recherche
            results = await client.search_content(criteria)

            end_time = time.time()
            execution_time = end_time - start_time

            # Afficher les résultats
            logger.info(f"✅ Recherche parallèle terminée en {execution_time:.3f} secondes")
            logger.info(f"📊 Nombre de résultats: {len(results)}")

            if results:
                logger.info("📄 Premiers résultats:")
                for i, content in enumerate(results[:3]):
                    logger.info(f"  {i+1}. {content.title} (ID: {content.id})")

                if len(results) > 3:
                    logger.info(f"  ... et {len(results) - 3} autres résultats")
            else:
                logger.warning("Aucun résultat trouvé. Vérifiez les critères de recherche.")

    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        raise

async def test_sequential_vs_parallel():
    """Compare la pagination séquentielle et parallèle."""
    logger = logging.getLogger(__name__)

    try:
        # Critères de test
        criteria = SearchCriteria(
            spaces=["EXAMPLE"],
            types=["page"],
            max_results=100
        )

        logger.info("=== Comparaison séquentielle vs parallèle ===")

        # Test 1: Pagination séquentielle
        config_seq = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
            enable_parallel_pagination=False
        )

        logger.info("🔄 Test pagination séquentielle...")
        async with ConfluenceClient(config_seq) as client:
            start_time = time.time()
            results_seq = await client.search_content(criteria)
            seq_time = time.time() - start_time

        logger.info(f"  Séquentielle: {seq_time:.3f}s, {len(results_seq)} résultats")

        # Test 2: Pagination parallèle
        config_par = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
            enable_parallel_pagination=True,
            max_parallel_requests=3,
            parallel_pagination_threshold=50
        )

        logger.info("🚀 Test pagination parallèle...")
        async with ConfluenceClient(config_par) as client:
            start_time = time.time()
            results_par = await client.search_content(criteria)
            par_time = time.time() - start_time

        logger.info(f"  Parallèle: {par_time:.3f}s, {len(results_par)} résultats")

        # Comparaison
        if seq_time > 0 and par_time > 0:
            speedup = seq_time / par_time
            improvement = ((seq_time - par_time) / seq_time) * 100

            logger.info(f"📈 Résultats de la comparaison:")
            logger.info(f"  Facteur d'accélération: {speedup:.2f}x")
            logger.info(f"  Amélioration: {improvement:.1f}%")

            if len(results_seq) == len(results_par):
                logger.info("✅ Même nombre de résultats obtenus")
            else:
                logger.warning(f"⚠️  Différence: {len(results_seq)} vs {len(results_par)} résultats")

    except Exception as e:
        logger.error(f"❌ Erreur lors de la comparaison: {e}")
        raise

async def test_configuration_impact():
    """Teste l'impact de différentes configurations."""
    logger = logging.getLogger(__name__)

    try:
        criteria = SearchCriteria(
            spaces=["EXAMPLE"],
            max_results=200
        )

        logger.info("=== Test de l'impact des configurations ===")

        # Différentes configurations
        configs = [
            {"max_parallel": 2, "threshold": 100},
            {"max_parallel": 3, "threshold": 50},
            {"max_parallel": 5, "threshold": 50},
        ]

        for i, config_params in enumerate(configs):
            logger.info(f"🔧 Configuration {i+1}: {config_params['max_parallel']} parallèles, seuil {config_params['threshold']}")

            config = ConfluenceConfig(
                url="https://test.atlassian.net",
                pat_token=SecretStr("test_token"),
                enable_parallel_pagination=True,
                max_parallel_requests=config_params["max_parallel"],
                parallel_pagination_threshold=config_params["threshold"]
            )

            async with ConfluenceClient(config) as client:
                start_time = time.time()
                results = await client.search_content(criteria)
                execution_time = time.time() - start_time

                logger.info(f"  ⏱️  {execution_time:.3f}s, {len(results)} résultats")

            await asyncio.sleep(0.5)  # Pause entre les tests

    except Exception as e:
        logger.error(f"❌ Erreur lors du test de configuration: {e}")
        raise

async def main():
    """Fonction principale."""
    logger = logging.getLogger(__name__)

    logger.info("🚀 Démarrage des tests de pagination parallèle")

    try:
        # Test 1: Pagination parallèle basique
        await test_parallel_pagination()

        print("\n" + "="*60 + "\n")

        # Test 2: Comparaison des méthodes
        await test_sequential_vs_parallel()

        print("\n" + "="*60 + "\n")

        # Test 3: Impact des configurations
        await test_configuration_impact()

        logger.info("✅ Tous les tests de pagination parallèle terminés avec succès!")

    except Exception as e:
        logger.error(f"❌ Échec des tests: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
