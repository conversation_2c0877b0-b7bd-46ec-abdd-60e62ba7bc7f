#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test rapide de l'optimisation du client Confluence.
"""

import asyncio
import logging
import time

from pydantic import SecretStr

from ..client import ConfluenceClient
from ..config import ConfluenceConfig, SearchCriteria

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_optimized_search():
    """Test rapide de la recherche optimisée."""
    logger = logging.getLogger(__name__)

    try:
        # Configuration de test avec des valeurs mock
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
        )
        logger.info("Configuration de test créée")

        # Test avec context manager (recommandé)
        async with ConfluenceClient(config) as client:
            logger.info("=== Test de la recherche optimisée ===")

            # Critères de recherche simples
            criteria = SearchCriteria(
                spaces=["EXAMPLE"],  # Remplacez par un espace existant
                types=["page"],
                max_results=5
            )

            # Mesurer le temps d'exécution
            start_time = time.time()

            # Effectuer la recherche
            results = await client.search_content(criteria)

            end_time = time.time()
            execution_time = end_time - start_time

            # Afficher les résultats
            logger.info(f"✅ Recherche terminée en {execution_time:.3f} secondes")
            logger.info(f"📊 Nombre de résultats: {len(results)}")

            if results:
                logger.info("📄 Premiers résultats:")
                for i, content in enumerate(results[:3]):
                    logger.info(f"  {i+1}. {content.title} (ID: {content.id})")
            else:
                logger.warning("Aucun résultat trouvé. Vérifiez les critères de recherche.")

    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        raise

async def test_multiple_searches():
    """Test de recherches multiples avec session réutilisable."""
    logger = logging.getLogger(__name__)

    try:
        # Configuration de test avec des valeurs mock
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
        )

        async with ConfluenceClient(config) as client:
            logger.info("=== Test de recherches multiples ===")

            # Différents critères de recherche
            search_list = [
                SearchCriteria(types=["page"], max_results=3),
                SearchCriteria(types=["blogpost"], max_results=3),
            ]

            start_time = time.time()
            total_results = 0

            for i, criteria in enumerate(search_list):
                logger.info(f"Recherche {i+1}/{len(search_list)}...")
                results = await client.search_content(criteria)
                total_results += len(results)
                logger.info(f"  -> {len(results)} résultats")

            end_time = time.time()
            execution_time = end_time - start_time

            logger.info(f"✅ {len(search_list)} recherches terminées en {execution_time:.3f} secondes")
            logger.info(f"📊 Total des résultats: {total_results}")

    except Exception as e:
        logger.error(f"❌ Erreur lors du test multiple: {e}")
        raise

async def main():
    """Fonction principale."""
    logger = logging.getLogger(__name__)

    logger.info("🚀 Démarrage des tests d'optimisation du client Confluence")

    try:
        # Test 1: Recherche simple
        await test_optimized_search()

        print("\n" + "="*50 + "\n")

        # Test 2: Recherches multiples
        await test_multiple_searches()

        logger.info("✅ Tous les tests terminés avec succès!")

    except Exception as e:
        logger.error(f"❌ Échec des tests: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
