#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module HTTP.
"""

import unittest
import asyncio
import aiohttp
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from ..http import ResponseProcessor, RequestContext
from ..constants import APIConstants
from ..exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError
)


class TestRequestContext(unittest.TestCase):
    """Tests pour la classe RequestContext."""

    def test_request_context_creation(self):
        """Test de création d'un contexte de requête."""
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        self.assertEqual(context.method, "GET")
        self.assertEqual(context.endpoint, "/api/content")
        self.assertEqual(context.service_name, "test_service")
        self.assertFalse(context.download_mode)
        self.assertIsNone(context.headers)
        self.assertIsNone(context.params)

    def test_request_context_with_optional_params(self):
        """Test de création d'un contexte avec paramètres optionnels."""
        headers = {"Authorization": "Bearer token"}
        params = {"limit": 50}
        
        context = RequestContext(
            method="POST",
            endpoint="/api/search",
            service_name="search_service",
            download_mode=True,
            headers=headers,
            params=params
        )
        
        self.assertEqual(context.method, "POST")
        self.assertEqual(context.endpoint, "/api/search")
        self.assertEqual(context.service_name, "search_service")
        self.assertTrue(context.download_mode)
        self.assertEqual(context.headers, headers)
        self.assertEqual(context.params, params)


class TestResponseProcessor(unittest.TestCase):
    """Tests pour la classe ResponseProcessor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.processor = ResponseProcessor(self.logger)

    def test_response_processor_initialization(self):
        """Test de l'initialisation du processeur de réponse."""
        self.assertEqual(self.processor.logger, self.logger)

    async def test_process_response_success_200(self):
        """Test de traitement d'une réponse 200 réussie."""
        mock_response = AsyncMock()
        mock_response.status = 200
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        with patch.object(self.processor, '_handle_success_response') as mock_success:
            mock_success.return_value = {"result": "success"}
            
            result = await self.processor.process_response(mock_response, context)
            
            self.assertEqual(result, {"result": "success"})
            mock_success.assert_called_once_with(mock_response, context)

    async def test_process_response_error_status(self):
        """Test de traitement d'une réponse d'erreur."""
        mock_response = AsyncMock()
        mock_response.status = 404
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content/123",
            service_name="test_service"
        )
        
        with patch.object(self.processor, '_handle_error_response') as mock_error:
            mock_error.side_effect = ContentNotFoundError("Not found")
            
            with self.assertRaises(ContentNotFoundError):
                await self.processor.process_response(mock_response, context)
            
            mock_error.assert_called_once_with(mock_response, context)

    async def test_handle_success_response_download_mode(self):
        """Test de gestion d'une réponse réussie en mode téléchargement."""
        mock_response = AsyncMock()
        mock_response.read.return_value = b"binary data"
        
        context = RequestContext(
            method="GET",
            endpoint="/api/attachment/download",
            service_name="download_service",
            download_mode=True
        )
        
        result = await self.processor._handle_success_response(mock_response, context)
        
        self.assertEqual(result, b"binary data")
        mock_response.read.assert_called_once()

    async def test_handle_success_response_json(self):
        """Test de gestion d'une réponse JSON réussie."""
        mock_response = AsyncMock()
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_response.json.return_value = {"data": "test"}
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        result = await self.processor._handle_success_response(mock_response, context)
        
        self.assertEqual(result, {"data": "test"})
        mock_response.json.assert_called_once()

    async def test_handle_success_response_text(self):
        """Test de gestion d'une réponse texte réussie."""
        mock_response = AsyncMock()
        mock_response.headers = {'Content-Type': 'text/plain'}
        mock_response.text.return_value = "plain text response"
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        result = await self.processor._handle_success_response(mock_response, context)
        
        self.assertEqual(result, "plain text response")
        mock_response.text.assert_called_once()

    async def test_handle_error_response_401(self):
        """Test de gestion d'une erreur 401."""
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_response.text.return_value = "Unauthorized access"
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        with patch('confluence_rag.http.SecurityUtils.sanitize_error_message') as mock_sanitize:
            mock_sanitize.return_value = "Unauthorized access"
            
            with self.assertRaises(AuthenticationError) as cm:
                await self.processor._handle_error_response(mock_response, context)
            
            self.assertIn("Erreur d'authentification", str(cm.exception))

    async def test_handle_error_response_404(self):
        """Test de gestion d'une erreur 404."""
        mock_response = AsyncMock()
        mock_response.status = 404
        mock_response.text.return_value = "Not found"
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content/123",
            service_name="test_service"
        )
        
        with patch('confluence_rag.http.SecurityUtils.sanitize_error_message') as mock_sanitize:
            mock_sanitize.return_value = "Not found"
            
            with self.assertRaises(ContentNotFoundError) as cm:
                await self.processor._handle_error_response(mock_response, context)
            
            self.assertIn("/api/content/123", str(cm.exception))

    async def test_handle_error_response_429(self):
        """Test de gestion d'une erreur 429 (rate limit)."""
        mock_response = AsyncMock()
        mock_response.status = 429
        mock_response.text.return_value = "Rate limit exceeded"
        mock_response.headers = {'Retry-After': '60'}
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        with patch('confluence_rag.http.SecurityUtils.sanitize_error_message') as mock_sanitize:
            mock_sanitize.return_value = "Rate limit exceeded"
            
            with self.assertRaises(RateLimitExceededError) as cm:
                await self.processor._handle_error_response(mock_response, context)
            
            self.assertEqual(cm.exception.retry_after, 60)

    async def test_handle_error_response_generic(self):
        """Test de gestion d'une erreur générique."""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = "Internal server error"
        
        context = RequestContext(
            method="GET",
            endpoint="/api/content",
            service_name="test_service"
        )
        
        with patch('confluence_rag.http.SecurityUtils.sanitize_error_message') as mock_sanitize:
            mock_sanitize.return_value = "Internal server error"
            
            with self.assertRaises(APIError) as cm:
                await self.processor._handle_error_response(mock_response, context)
            
            self.assertEqual(cm.exception.status_code, 500)
            self.assertIn("500", str(cm.exception))

    def test_parse_retry_after_valid_integer(self):
        """Test de parsing d'un header Retry-After valide."""
        result = self.processor._parse_retry_after("120")
        self.assertEqual(result, 120)

    def test_parse_retry_after_invalid(self):
        """Test de parsing d'un header Retry-After invalide."""
        result = self.processor._parse_retry_after("invalid")
        self.assertEqual(result, APIConstants.DEFAULT_RETRY_AFTER)

    def test_parse_retry_after_none(self):
        """Test de parsing d'un header Retry-After None."""
        result = self.processor._parse_retry_after(None)
        self.assertEqual(result, APIConstants.DEFAULT_RETRY_AFTER)

    def test_parse_retry_after_empty(self):
        """Test de parsing d'un header Retry-After vide."""
        result = self.processor._parse_retry_after("")
        self.assertEqual(result, APIConstants.DEFAULT_RETRY_AFTER)


if __name__ == '__main__':
    unittest.main()
