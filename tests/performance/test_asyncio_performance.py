#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance pour analyser l'impact de l'architecture mixte asyncio/synchrone.
"""

import asyncio
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch
import pytest

from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret


class TestAsyncioPerformance:
    """Tests de performance pour l'architecture mixte asyncio/synchrone."""
    
    @pytest.fixture
    def mock_config_with_secret(self):
        """Mock de ConfigWithSecret pour les tests."""
        config = Mock(spec=ConfigWithSecret)
        config.get_confluence_credentials.return_value = {
            'username': '<EMAIL>',
            'api_token': 'test_token'
        }
        return config
    
    @pytest.fixture
    def sample_source(self):
        """Source de test."""
        return SourceBean(
            id=1,
            code="TEST_SOURCE",
            label="Test Source",
            domain_code="TEST_DOMAIN",
            perimeter_code="TEST_PERIMETER",
            src_type="confluence",
            configuration='{"spaces": ["TEST"], "max_results": 10}',
            last_load_time=1726156483,
            load_interval=24,
            force_embedding=False
        )
    
    def test_current_implementation_blocking_behavior(self, mock_config_with_secret, sample_source):
        """Test pour mesurer le comportement bloquant de l'implémentation actuelle."""
        
        # Simuler un délai dans l'appel asyncio
        async def mock_search_content(criteria):
            await asyncio.sleep(0.1)  # Simuler un appel réseau
            return []
        
        with patch.dict('os.environ', {
            'CONFLUENCE_URL': 'https://test.atlassian.net',
            'DEFAULT_SPACE_KEY': 'TEST'
        }):
            with patch('kbotloadscheduler.loader.confluence.confluence_loader.SyncOrchestrator') as mock_orchestrator_class:
                # Configuration du mock
                mock_orchestrator = Mock()
                mock_orchestrator.client.search_content = mock_search_content
                mock_orchestrator_class.return_value = mock_orchestrator
                
                loader = ConfluenceLoader(mock_config_with_secret)
                
                # Mesurer le temps d'exécution
                start_time = time.time()
                
                # Simuler plusieurs appels simultanés (comme dans un environnement de production)
                def call_loader():
                    try:
                        return loader.get_document_list(sample_source)
                    except Exception as e:
                        print(f"Erreur dans call_loader: {e}")
                        return []
                
                # Test avec ThreadPoolExecutor pour simuler des appels concurrents
                with ThreadPoolExecutor(max_workers=3) as executor:
                    futures = [executor.submit(call_loader) for _ in range(3)]
                    results = [future.result() for future in futures]
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                print(f"Temps d'exécution pour 3 appels concurrents: {execution_time:.2f}s")
                
                # Vérifier que tous les appels ont réussi
                assert len(results) == 3
                assert all(isinstance(result, list) for result in results)
                
                # Le temps devrait être proche de 0.3s (3 * 0.1s) car les appels sont bloquants
                # Si c'était vraiment parallèle, ce serait proche de 0.1s
                assert execution_time >= 0.25  # Tolérance pour les variations système
    
    def test_improved_implementation_with_asyncio_run(self, mock_config_with_secret, sample_source):
        """Test de l'implémentation améliorée avec asyncio.run()."""
        
        async def mock_search_content(criteria):
            await asyncio.sleep(0.1)
            return []
        
        # Implémentation améliorée simulée
        class ImprovedConfluenceLoader(ConfluenceLoader):
            def get_document_list(self, source):
                """Version améliorée avec asyncio.run()"""
                try:
                    return asyncio.run(self._async_get_document_list(source))
                except Exception as e:
                    print(f"Erreur dans get_document_list amélioré: {e}")
                    return []
            
            async def _async_get_document_list(self, source):
                """Logique asynchrone extraite"""
                # Simuler la logique asynchrone
                await asyncio.sleep(0.1)
                return []
        
        with patch.dict('os.environ', {
            'CONFLUENCE_URL': 'https://test.atlassian.net',
            'DEFAULT_SPACE_KEY': 'TEST'
        }):
            loader = ImprovedConfluenceLoader(mock_config_with_secret)
            
            start_time = time.time()
            
            def call_improved_loader():
                return loader.get_document_list(sample_source)
            
            # Test avec ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = [executor.submit(call_improved_loader) for _ in range(3)]
                results = [future.result() for future in futures]
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"Temps d'exécution amélioré pour 3 appels concurrents: {execution_time:.2f}s")
            
            # Vérifier que tous les appels ont réussi
            assert len(results) == 3
            assert all(isinstance(result, list) for result in results)
    
    def test_thread_safety_analysis(self, mock_config_with_secret, sample_source):
        """Test pour analyser la sécurité des threads."""
        
        with patch.dict('os.environ', {
            'CONFLUENCE_URL': 'https://test.atlassian.net',
            'DEFAULT_SPACE_KEY': 'TEST'
        }):
            loader = ConfluenceLoader(mock_config_with_secret)
            
            # Variables partagées pour détecter les problèmes de concurrence
            results = []
            errors = []
            
            def worker():
                try:
                    # Simuler un appel qui pourrait causer des problèmes de concurrence
                    result = loader._create_confluence_config(sample_source)
                    results.append(result)
                except Exception as e:
                    errors.append(str(e))
            
            # Lancer plusieurs threads simultanément
            threads = [threading.Thread(target=worker) for _ in range(5)]
            
            for thread in threads:
                thread.start()
            
            for thread in threads:
                thread.join()
            
            # Analyser les résultats
            print(f"Résultats réussis: {len(results)}")
            print(f"Erreurs: {len(errors)}")
            
            if errors:
                print("Erreurs détectées:")
                for error in errors:
                    print(f"  - {error}")
            
            # Tous les appels devraient réussir sans erreurs de concurrence
            assert len(errors) == 0, f"Erreurs de concurrence détectées: {errors}"
            assert len(results) == 5
    
    def test_memory_usage_analysis(self, mock_config_with_secret, sample_source):
        """Test pour analyser l'utilisation mémoire."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with patch.dict('os.environ', {
            'CONFLUENCE_URL': 'https://test.atlassian.net',
            'DEFAULT_SPACE_KEY': 'TEST'
        }):
            loader = ConfluenceLoader(mock_config_with_secret)
            
            # Simuler plusieurs créations/destructions de boucles d'événements
            for i in range(10):
                try:
                    # Simuler la création de configuration (sans appels réseau)
                    config = loader._create_confluence_config(sample_source)
                    assert config is not None
                except Exception as e:
                    print(f"Erreur lors de l'itération {i}: {e}")
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"Mémoire initiale: {initial_memory:.2f} MB")
            print(f"Mémoire finale: {final_memory:.2f} MB")
            print(f"Augmentation: {memory_increase:.2f} MB")
            
            # L'augmentation de mémoire devrait être raisonnable (< 50 MB pour ce test)
            assert memory_increase < 50, f"Augmentation de mémoire excessive: {memory_increase:.2f} MB"


if __name__ == "__main__":
    # Exécuter les tests de performance
    pytest.main([__file__, "-v", "-s"])
